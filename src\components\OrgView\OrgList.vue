<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { Formatter } from '@/script';
import { Repos, type MomOrganization } from '../../../../xtrade-sdk/dist';

const repoInstance = new Repos.AdminRepo();

// 基础列定义
const columns: ColumnDefinition<MomOrganization> = [
  { key: 'id', title: '机构ID', width: 100, sortable: true },
  { key: 'orgName', title: '机构名', width: 100, sortable: true },
  { key: 'domain', title: '域名', width: 100, sortable: true },
  { key: 'contract', title: '联系人', width: 100, sortable: true },
  { key: 'phone', title: '电话', width: 100, sortable: true },
  { key: 'email', title: '邮件', width: 200, sortable: true },
  { key: 'status', title: '状态', width: 100, sortable: true }, // （1启用，0禁用）
  { key: 'introduction', title: '机构简介', width: 300, sortable: true },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  { key: 'configuration', title: '机构配置', width: 100, sortable: true }, // （JSON格式）
];

// 行操作
const rowActions: RowAction<MomOrganization>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function editRow(row: MomOrganization) {
  console.log('edit', row);
}

function deleteRow(row: MomOrganization) {
  console.log('delete', row);
}

const records = shallowRef<MomOrganization[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = (await repoInstance.QueryOrgs()).data || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建机构</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
