<template>
  <el-tabs v-model="activeTab" class="typical-tabs">
    <el-tab-pane :name="tabs.product">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-consist"></i>
            <span pl-8>产品信息</span>
          </span>
        </slot>
      </template>
      <ProductBasicInfoForm
        v-model="contextProduct"
        @cancel="cancelEditBasic"
      ></ProductBasicInfoForm>
    </el-tab-pane>
    <el-tab-pane :name="tabs.account">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-document"></i>
            <span pl-8>账号管理</span>
          </span>
        </slot>
      </template>
      <AccountList></AccountList>
    </el-tab-pane>
    <el-tab-pane :name="tabs.user">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-user"></i>
            <span pl-8>人员/流程设置</span>
          </span>
        </slot>
      </template>
      <div h-500 line-height-500 text-center>user</div>
    </el-tab-pane>
    <el-tab-pane :name="tabs.risk">
      <template #label>
        <slot name="label">
          <span>
            <i class="iconfont icon-bell"></i>
            <span pl-8>风控设置</span>
          </span>
        </slot>
      </template>
      <div h-500 line-height-500 text-center>risk</div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ProductBasicInfoForm from './ProductBasicInfoForm.vue';
import AccountList from '../AccountView/AccountList.vue';
import type { ProductInfo } from '@/types';

const contextProduct = defineModel<ProductInfo | null>();
const tabs = { product: 'p', account: 'a', user: 'u', risk: 'r' };
const activeTab = ref(tabs.product);

const emitter = defineEmits<{
  cancel: [];
}>();

function cancelEditBasic() {
  emitter('cancel');
}
</script>

<style scoped></style>
