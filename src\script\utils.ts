export interface FormatNumberOptions {
  /** 保留的小数位数，默认为2 */
  fix?: number;
  /** 是否转换为百分比格式，默认为false */
  percent?: boolean;
  /** 是否添加正负号前缀，默认为false */
  prefix?: boolean;
  /** 是否去掉末尾多余的0，默认为false */
  trim?: boolean;
  /** 默认值，默认为空字符串 */
  default?: string;
  /**是否缩写大数字(万/亿)，默认为false */
  abbreviate?: boolean;
  /** 是否添加千位分隔符，默认为false */
  separator?: boolean;
}

function isJson(obj: any) {
  const class2type = {};
  const toString = class2type.toString;
  const hasOwn = class2type.hasOwnProperty;
  const fnToString = hasOwn.toString;
  const ObjectFunctionString = fnToString.call(Object);

  if (!obj || toString.call(obj) !== '[object Object]') {
    return false;
  }

  const proto = Object.getPrototypeOf(obj);

  // Objects with no prototype (e.g., `Object.create( null )`) are plain
  if (!proto) {
    return true;
  }

  // Objects with prototype are plain if they were constructed by a global Object function
  const ctor = hasOwn.call(proto, 'constructor') && proto.constructor;
  return typeof ctor === 'function' && fnToString.call(ctor) === ObjectFunctionString;
}

function deepAssign(target: any, ...sources: any[]) {
  if (!isJson(target)) {
    return target;
  } else if (!sources.length) {
    return target;
  }

  while (sources.length > 0) {
    const source = sources.shift();
    if (!isJson(source)) {
      continue;
    }

    for (const key in source) {
      const sourceValue = source[key];
      const targetValue = target[key];

      if (isJson(sourceValue) && isJson(targetValue)) {
        // 如果都是对象，递归合并
        deepAssign(targetValue, sourceValue);
      } else if (isJson(sourceValue)) {
        // 如果源值是对象但目标不是，则目标设为空对象再赋值
        target[key] = deepAssign({}, sourceValue);
      } else {
        // 否则直接赋值
        target[key] = sourceValue;
      }
    }
  }

  return target;
}

/**
 * 工具类
 */
class Utils {
  /** 休眠 */
  static async sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 从localStorage中获取指定名称的数据
   * 如果数据不存在，返回默认值或null
   *
   * @param name 存储名称
   * @param defaultValue 可选的默认值
   * @returns 存储的数据，如果不存在则返回默认值或null
   */
  static getLocal<T>(name: string): T | null;
  static getLocal<T>(name: string, defaultValue: T): T;
  static getLocal<T>(name: string, defaultValue?: T): T | null {
    const val = localStorage.getItem(name);
    if (val) {
      try {
        return JSON.parse(val);
      } catch (ex) {
        return val as any as T;
      }
    } else if (defaultValue !== undefined) {
      return defaultValue;
    } else {
      return null;
    }
  }

  /**
   * 设置本地存储项
   *
   * 该方法用于将数据存储到浏览器的本地存储中它支持存储字符串或对象类型的数据
   * 如果提供的值为undefined或null，则会从本地存储中移除相应的项
   *
   * @param name 存储项的名称
   * @param value 存储项的值，可以是字符串、对象或undefined/null
   */
  static setLocal(name: string, value: string | object | undefined) {
    if (value === undefined || value === null) {
      localStorage.removeItem(name);
    } else if (typeof value === 'object') {
      localStorage.setItem(name, JSON.stringify(value));
    } else {
      localStorage.setItem(name, value);
    }
  }

  /**
   * 创建一个防抖函数
   *
   * 防抖函数会在一定时间内（wait毫秒）只执行一次回调。
   * 如果在等待时间内再次调用，会重置计时器。
   *
   * @param func 需要进行防抖处理的函数
   * @param wait 等待时间，单位为毫秒
   * @returns 返回一个新的防抖函数
   *
   * @example
   * const debouncedFn = Utils.debounce((value: string) => {
   *   console.log(value);
   * }, 300);
   *
   * // 多次快速调用，只会在最后一次调用后300ms执行一次
   * debouncedFn("test1");
   * debouncedFn("test2");
   * debouncedFn("test3"); // 只有这次会在300ms后执行
   */
  static debounce<T extends (...args: Parameters<T>) => ReturnType<T>>(func: T, wait: number) {
    let timeout: NodeJS.Timeout | undefined;
    return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * 创建一个节流函数
   *
   * 节流函数会确保在指定的时间间隔内最多只执行一次回调函数。
   * 如果在等待时间内多次调用，只有第一次调用会被执行。
   *
   * @param func 需要进行节流处理的函数
   * @param wait 等待时间，单位为毫秒
   * @returns 返回一个新的节流函数
   *
   * @example
   * const throttledFn = Utils.throttle((value: string) => {
   *   console.log(value);
   * }, 300);
   *
   * // 多次快速调用，每300ms最多执行一次
   * throttledFn("test1"); // 会执行
   * throttledFn("test2"); // 被忽略
   * throttledFn("test3"); // 被忽略
   */
  static throttle<T extends (...args: Parameters<T>) => ReturnType<T>>(func: T, wait: number) {
    let timeout: NodeJS.Timeout | undefined;
    return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
      if (!timeout) {
        timeout = setTimeout(() => {
          func.apply(this, args);
          timeout = undefined;
        }, wait);
      }
    };
  }

  /**
   * 生成一个随机字符串
   *
   * @param length 字符串长度，默认为16
   * @returns 生成的随机字符串
   */
  static randomString(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const maxPos = chars.length;
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return result;
  }

  /**
   * 深度克隆对象或数组
   *
   * @param obj 要克隆的对象或数组
   * @returns 深度克隆后的对象或数组
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => Utils.deepClone(item)) as T;
    }

    const clonedObj = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = Utils.deepClone(obj[key]);
      }
    }

    return clonedObj;
  }

  static deepAssign<T = any>(target: any, ...sources: T[]) {
    return deepAssign(target, ...sources) as T;
  }

  static isJson(obj: any) {
    return isJson(obj);
  }

  /**
   * 格式化数字为字符串
   *
   * @param num 要格式化的数字
   * @param options 格式化选项
   * @param options.fix 保留的小数位数，默认为2
   * @param options.percent 是否转换为百分比格式，默认为false
   * @param options.prefix 是否添加正负号前缀，默认为false
   * @param options.trim 是否去掉末尾多余的0，默认为false
   * @param options.abbreviate 是否缩写大数字(万/亿)，默认为false
   * @param options.separator 是否添加千位分隔符，默认为false
   * @returns 格式化后的字符串
   *
   * @example
   * // 基础用法
   * Utils.formatNumber(12.345) // "12.35"
   *
   * // 百分比格式
   * Utils.formatNumber(0.1234, { percent: true }) // "12.34%"
   *
   * // 添加正负号
   * Utils.formatNumber(-12.34, { prefix: true }) // "-12.34"
   * Utils.formatNumber(12.34, { prefix: true }) // "+12.34"
   *
   * // 保留末尾的0
   * Utils.formatNumber(12.3, { trim: false }) // "12.30"
   *
   * // 数字缩写
   * Utils.formatNumber(12345, { abbreviate: true }) // "1.23万"
   * Utils.formatNumber(123456789, { abbreviate: true }) // "1.23亿"
   *
   * // 添加千位分隔符
   * Utils.formatNumber(5123456.789, { separator: true }) // "5,123,456.79"
   */
  static formatNumber(num: number | unknown, options?: FormatNumberOptions): string {
    const {
      fix = 2,
      percent = false,
      prefix = false,
      trim = false,
      abbreviate = false,
      separator = false,
    } = options || {};

    if (!this.isValidNumber(num)) {
      return options?.default || '';
    }

    let result = num as number;
    if (percent) {
      result *= 100;
    }

    // 处理数字缩写
    let unit = '';
    if (abbreviate) {
      if (Math.abs(result) >= 100000000) {
        result = result / 100000000;
        unit = '亿';
      } else if (Math.abs(result) >= 10000) {
        result = result / 10000;
        unit = '万';
      }
    }

    let formatted = result.toFixed(fix);
    if (trim) {
      formatted = parseFloat(formatted).toString();
    }

    // 添加千位分隔符
    if (separator) {
      const parts = formatted.split('.');
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      formatted = parts.join('.');
    }

    // 添加单位
    if (unit) {
      formatted += unit;
    }

    if (percent) {
      formatted += '%';
    }

    if (prefix) {
      if (result > 0) {
        formatted = '+' + formatted;
      } else if (result < 0) {
        formatted = '-' + formatted.slice(1);
      }
    }

    return formatted;
  }

  /**
   * 判断数字是否是正数
   *
   * @param num 要判断的数字
   * @returns 如果是正数返回true，否则返回false
   */
  static isPositive(num: number): boolean {
    return num > 0;
  }

  /**
   * 根据数值返回对应的颜色样式类名
   *
   * @param val - 要判断的值，可以是任意类型
   * @param options - 可选配置项
   * @param options.defaultClass - 当值为基准值时使用的默认样式类名，默认为'text-white'
   * @param options.prefix - 样式类名前缀，默认为'c'
   * @param options.baseline - 判断颜色的基准值，默认为0
   * @returns 返回对应的颜色样式类名
   *
   * @example
   * // 基础用法
   * Utils.getColorClass(100) // 返回 "c-[var(--g-red)]"
   * Utils.getColorClass(-50) // 返回 "c-[var(--g-green)]"
   * Utils.getColorClass(0)  // 返回 "text-white"
   *
   * // 自定义前缀
   * Utils.getColorClass(100, { prefix: 'bg' }) // 返回 "bg-[var(--g-red)]"
   *
   * // 自定义默认类名
   * Utils.getColorClass(0, { defaultClass: 'text-gray' }) // 返回 "text-gray"
   *
   * // 自定义基准值
   * Utils.getColorClass(80, { baseline: 100 }) // 返回 "c-[var(--g-green)]"
   */
  static getColorClass(
    val: unknown,
    options?: { defaultClass?: string; prefix?: string; baseline?: number },
  ) {
    const { defaultClass = 'text-white', prefix = 'c', baseline = 0 } = options || {};
    if (!this.isValidNumber(val)) {
      return '';
    } else {
      if (val === baseline) {
        return defaultClass;
      }
      const color = (val as number) > baseline ? 'red' : 'green';
      return `${prefix}-[var(--g-${color})]`;
    }
  }

  /**
   * 判断传入的参数是否是有效数字
   *
   * @param value 要判断的值
   * @returns 如果是有效数字返回true，否则返回false
   */
  static isValidNumber(value: unknown): value is number {
    if (typeof value === 'number') {
      return !isNaN(value);
    }
    if (typeof value === 'string' && value.trim() !== '') {
      return !isNaN(Number(value)) && isFinite(Number(value));
    }
    return false;
  }
  /**
   * 将TypeScript枚举转换为数组
   *
   * @param enumObj 要转换的枚举对象
   * @returns 转换后的数组，每个元素包含label和value属性
   *
   * @example
   * enum Direction { Up = 1, Down = -1 }
   * Utils.enumToArray(Direction) // 返回 [{label: 'Up', value: 1}, {label: 'Down', value: -1}]
   *
   * enum Color { Red = 'red', Blue = 'blue' }
   * Utils.enumToArray(Color) // 返回 [{label: 'Red', value: 'red'}, {label: 'Blue', value: 'blue'}]
   */
  static enumToArray<T extends Record<string, string | number>>(enumObj: T) {
    const arr: Array<{ label: string; value: T[keyof T] }> = [];
    const type = Object.values(enumObj).some(val => typeof val === 'number') ? 'number' : 'string';
    for (const label in enumObj) {
      if (typeof enumObj[label] === type) {
        const value = enumObj[label];
        arr.push({ label, value });
      }
    }
    return arr;
  }

  /**
   * to compare two values
   * @param asc sorting, by default = ascending
   * @returns a > b = 1; a < b = -1; a = b = 0;
   */
  static compare(a: any, b: any, asc = true) {
    const Regex = {
      all_cn: /^[\u4E00-\u9FA5]+$/,
      has_cn: /[\u4E00-\u9FA5]+/,
      no_cn: /^[a-zA-Z0-9~!@#$%^&*()-_=+[\]\\<>,;"'./|]+$/,
      all_en: /^[a-zA-Z]+$/,
      all_digit: /^[0-9]+$/,
    };

    const bigger = 1,
      same = 0,
      smaller = -1;
    const flag = asc ? 1 : -1;
    let result = same;

    if (a === b) {
      return same;
    }

    const _1st = {
      isempty: a === undefined || a === null || (typeof a == 'string' && a.trim().length == 0),
      isnum: typeof a == 'number',
      isstr: typeof a == 'string',
    };

    const _2nd = {
      isempty: b === undefined || b === null || (typeof b == 'string' && b.trim().length == 0),
      isnum: typeof b == 'number',
      isstr: typeof b == 'string',
    };

    const tostr = (value: object) => {
      return value.toString();
    };

    const is_cn = (value: string) => {
      return Regex.all_cn.test(value);
    };

    if (_1st.isempty) {
      result = _2nd.isempty ? same : smaller;
    } else if (_1st.isnum) {
      if (_2nd.isempty) {
        result = bigger;
      } else if (_2nd.isnum) {
        result = a > b ? bigger : a < b ? smaller : same;
      } else if (_2nd.isstr) {
        result = tostr(a).localeCompare(b);
      }
    } else if (_1st.isstr) {
      if (_2nd.isempty) {
        result = bigger;
      } else if (_2nd.isnum) {
        result = a.localeCompare(tostr(b));
      } else if (_2nd.isstr) {
        const len_a = a.length;
        const len_b = b.length;
        const shorter = Math.min(len_a, len_b);

        for (let cur = 0; cur < shorter; cur++) {
          const c1 = a[cur];
          const c2 = b[cur];

          if (c1 != c2) {
            if (is_cn(c1)) {
              if (is_cn(c2)) {
                result = c1.localeCompare(c2, 'zh-CN');
              } else {
                result = bigger;
              }
            } else {
              if (is_cn(c2)) {
                result = smaller;
              } else {
                result = c1.localeCompare(c2);
              }
            }

            break;
          }
        }

        if (result == same) {
          result = len_a > len_b ? bigger : len_a < len_b ? smaller : same;
        }
      }
    }

    return result * flag;
  }

  static grandSearch<T = any>(
    data: Record<string, T>[],
    options: { keyword: string; fields?: string[] },
  ): Record<string, T>[] {
    const { keyword, fields } = options || {};

    if (!Array.isArray(data) || data.length == 0 || !keyword) {
      return data;
    }

    const lowered = keyword.toLowerCase();
    const has_fields = Array.isArray(fields) && fields.length > 0;

    if (has_fields) {
      return data.filter(item => {
        const values = fields.map(field => item[field]);
        return values.some(value => {
          return value?.toString().toLowerCase().includes(lowered);
        });
      });
    } else {
      return data.filter(item => {
        return Object.values(item).some(value => {
          return value?.toString().toLowerCase().includes(lowered);
        });
      });
    }
  }

  /**
   * to delete(remove) some elements from an array
   * @param arr the target array
   */
  static remove<T = any>(arr: Array<T>, predict: (ele: T) => boolean) {
    const indexes = [] as number[];
    arr.forEach((ele, ele_idx) => predict(ele) === true && indexes.push(ele_idx));
    while (indexes.length > 0) {
      arr.splice(indexes.pop()!, 1);
    }
  }

  /**
   * 使用异或运算加密字符串
   *
   * @param originalContent 需要加密的原始字符串
   * @returns 加密后的字符串，如果输入为空则返回空字符串
   *
   * @example
   * Utils.aesEncrypt('hello') // 返回加密后的字符串
   */
  static aesEncrypt(originalContent: string): string {
    if (!originalContent?.trim()) {
      return '';
    }

    const key = 'DaBingGe';
    const snNum: number[] = [];
    let result = '';

    // 使用异或运算加密每个字符
    for (let i = 0, j = 0; i < originalContent.length; i++, j = (j + 1) % key.length) {
      snNum.push(originalContent.charCodeAt(i) ^ key.charCodeAt(j));
    }

    // 将加密后的数字转换为固定长度的字符串
    for (const num of snNum) {
      result += num.toString().padStart(3, '0');
    }

    return result;
  }

  /**
   * 解密由aesEncrypt加密的字符串
   *
   * @param encryptedContent 加密后的字符串
   * @returns 解密后的原始字符串，如果输入为空则返回空字符串
   *
   * @example
   * const encrypted = Utils.aesEncrypt('hello');
   * Utils.aesDecrypt(encrypted) // 返回 'hello'
   */
  static aesDecrypt(encryptedContent: string): string {
    if (!encryptedContent?.trim()) {
      return '';
    }

    const key = 'DaDiaoGe666';
    const snNum = [];
    let result = '';

    // 每3个字符解析为一个数字
    for (let i = 0; i < encryptedContent.length; i += 3) {
      snNum.push(parseInt(encryptedContent.slice(i, i + 3), 10));
    }

    // 使用异或运算解密每个字符
    for (let i = 0, j = 0; i < snNum.length; i++, j = (j + 1) % key.length) {
      result += String.fromCharCode(snNum[i] ^ key.charCodeAt(j));
    }

    return result;
  }
}

export default Utils;
