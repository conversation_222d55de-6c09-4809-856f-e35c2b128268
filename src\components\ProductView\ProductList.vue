<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import TutorialPanel from '../ProductView/TutorialPanel.vue';
import { onMounted, shallowRef, useTemplateRef, inject, reactive } from 'vue';
import { TableV2SortOrder, ElSwitch } from 'element-plus';
import {
  getEmptyTableColumnConfig,
  type ColumnDefinition,
  type ProductInfo,
  type RowAction,
} from '@/types';
import { ProductService } from '@/api';
import { Formatter, Utils } from '@/script';
import { TABLE_COLUMN_SELECT_KEY } from '@/keys';

interface CellRenderParam {
  rowData: ProductInfo;
  cellData: any;
}

const aa = {} as any as ProductInfo;
console.log(aa.marketValue);

// 基础列定义
const columns: ColumnDefinition<ProductInfo> = [
  { key: 'fundName', title: '基金名字', width: 180, sortable: true, cellRenderer: renderNameCol },
  {
    key: 'navRealTime',
    title: '最新净值',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: renderRealtimeNavCol,
  },
  {
    key: 'nav',
    title: '累计净值',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: renderNavCol,
  },
  {
    key: 'fundAccounts',
    title: '产品份额',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: renderShareCol,
  },
  {
    key: 'balance',
    title: '总资产',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: render2Thousands,
  },
  {
    key: 'id',
    title: '收益率',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: renderWithNull,
  },
  {
    key: 'id',
    title: '总负债',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: renderWithNull,
  },
  {
    key: 'marketValue',
    title: '证券总资产',
    width: 90,
    sortable: true,
    align: 'center',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
  {
    key: 'marketValue',
    title: '证券总市值',
    width: 90,
    sortable: true,
    align: 'center',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
  {
    key: 'marketValue',
    title: '股票总市值',
    width: 90,
    sortable: true,
    align: 'center',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
  {
    key: 'marketValue',
    title: '期权总市值',
    width: 90,
    sortable: true,
    align: 'center',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
  {
    key: 'id',
    title: '权益占比',
    width: 90,
    sortable: true,
    align: 'center',
    cellRenderer: renderPositionPercentage,
  },

  { key: 'orgId', title: '机构ID', width: 100, sortable: true },
  { key: 'orgName', title: '机构名称', width: 100, sortable: true },
  // { key: 'fundCode', title: '基金外部编码', width: 110, sortable: true },
  { key: 'amacCode', title: '协会备案号', width: 100, sortable: true },
  { key: 'establishedDay', title: '基金成立日期', width: 110, sortable: true },
  { key: 'fundOrganization', title: '管理机构', width: 100, sortable: true },
  { key: 'fundManager', title: '基金经理', width: 100, sortable: true },
  { key: 'fundType', title: '基金类型', width: 100, sortable: true }, // （1真实，2虚拟，3组合）
  { key: 'basisReference', title: '业绩比较基准', width: 110, sortable: true },
  {
    key: 'valuation',
    title: '估值方式',
    width: 100,
    sortable: true,
    cellRenderer: renderValuation,
  },
  {
    key: 'closedFlag',
    title: '是否清盘',
    width: 110,
    sortable: true,
    cellRenderer: renderClosedFlag,
  },
  {
    key: 'riskEnable',
    title: '是否启用风控',
    width: 110,
    sortable: true,
    cellRenderer: renderRiskEnable,
  },
  // {
  //   key: 'createTime',
  //   title: '创建时间',
  //   width: 150,
  //   sortable: true,
  //   cellRenderer: formatDateTime,
  // },
  // {
  //   key: 'updateTime',
  //   title: '更新时间',
  //   width: 150,
  //   sortable: true,
  //   cellRenderer: formatDateTime,
  // },
  // { key: 'createUserId', title: '创建人ID', width: 100, sortable: true },
  // { key: 'createUserName', title: '创建人名称', width: 110, sortable: true },
  // { key: 'remark', title: '备注', width: 100, sortable: true },
];

// 行操作
const rowActions: RowAction<ProductInfo>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editProduct(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const tableCfgParams = inject(TABLE_COLUMN_SELECT_KEY, reactive(getEmptyTableColumnConfig()));
const tutorial = reactive({
  visible: false,
  contextProduct: null as ProductInfo | null,
});

function createProduct() {
  editProduct(null);
}

function editProduct(target: ProductInfo | null) {
  console.log('to create or edit product', Utils.deepClone(target));
  tutorial.visible = true;
  tutorial.contextProduct = target;
}

function deleteRow(row: ProductInfo) {
  console.log('delete', row);
}

const records = shallowRef<ProductInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

function renderHeader(params: any) {
  const title = params.column.title! as string;
  return (
    <span>
      <span>{title.substring(0, 2)}</span>
      <br />
      <span>{title.substring(2)}</span>
    </span>
  );
}

function renderNameCol(params: CellRenderParam) {
  const { fundName, strategyType } = params.rowData;
  return (
    <div class="fund-name-cell">
      <div class="fund-name toe">{fundName}</div>
      <div class="fund-strategy toe">{strategyType}</div>
    </div>
  );
}

function renderRealtimeNavCol(params: CellRenderParam) {
  const { navRealTime } = params.rowData;
  const date = Formatter.formatDateTime(new Date(), 'yy.MM.dd');
  return (
    <span>
      <span class="realtime-nav">
        {typeof navRealTime == 'number' ? navRealTime.toFixed(4) : '--'}
      </span>
      <br />
      <span class="realtime-nav-date">{date}</span>
    </span>
  );
}

function renderNavCol(params: CellRenderParam) {
  const { nav } = params.rowData;
  return <span>{typeof nav == 'number' ? nav.toFixed(4) : '--'}</span>;
}

function renderShareCol(params: CellRenderParam) {
  const { fundAccounts } = params.rowData;
  const share = fundAccounts.reduce((acc, cur) => acc + cur.fundShare, 0);
  return <span>{Formatter.thousands(share)}</span>;
}

function render2Thousands(params: CellRenderParam) {
  return <span>{Formatter.thousands(params.cellData)}</span>;
}

function renderWithNull() {
  return <span>--</span>;
}

function renderPositionPercentage(params: CellRenderParam) {
  const { marketValue, balance } = params.rowData;
  return <span>{balance == 0 ? 0 : ((marketValue * 100) / balance).toFixed(2)}%</span>;
}

function renderValuation(params: CellRenderParam) {
  const { valuation } = params.rowData;
  return <span>{valuation == 0 ? 'T+0' : valuation == 1 ? 'T+1' : '不估值'}</span>;
}

function renderClosedFlag(params: CellRenderParam) {
  return (
    <span>
      {!params.rowData.closedFlag ? (
        <span class="pr-5 color-green">运行中</span>
      ) : (
        <span class="pr-5 color-red">已清盘</span>
      )}
      <ElSwitch v-model={params.rowData.closedFlag} />
    </span>
  );
}

function renderRiskEnable(params: CellRenderParam) {
  return (
    <span>
      {!params.rowData.riskEnable ? (
        <span class="pr-5 color-green">已开启</span>
      ) : (
        <span class="pr-5 color-red">已关闭</span>
      )}
      <ElSwitch v-model={params.rowData.riskEnable} />
    </span>
  );
}

// function format2Percentage(params: CellRenderParam) {
//   return <span>{Formatter.thousands(params.rowData.closeProfit)}</span>;
// }

// function formatDateTime(params: CellRenderParam) {
//   return <span>{Formatter.formatDateTime(params.cellData)}</span>;
// }

function configColumn() {
  Object.assign(tableCfgParams, {
    name: '产品管理',
    columns: columns.map(x => ({ title: x!.title || '', datakey: x!.key })),
    selected: [],
    callback: (selected: string[]) => {
      console.log('selected columns', selected);
    },
  });
}

async function request() {
  records.value = (await ProductService.getProducts()) || [];
  console.log('records', Utils.deepClone(records.value));
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="product-list-view" w-full>
    <VirtualizedTable
      ref="tableRef"
      search-placeholder="搜索产品"
      :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="records"
      :row-actions="rowActions"
      :row-action-width="170"
      :row-height="60"
      :header-row-height="45"
      select
      fixed
    >
      <template #actions>
        <div class="actions" flex aic>
          <el-button link size="small" class="typical-text-button" @click="configColumn">
            <i class="iconfont icon-setting"></i>
            <span>列配置</span>
          </el-button>
          <el-button link size="small" class="typical-text-button">
            <i class="iconfont icon-download"></i>
            <span>下载</span>
          </el-button>
          <el-button type="primary" @click="createProduct">
            <i class="iconfont icon-add-new" mr-5></i>
            <span>新建产品</span>
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
    <el-dialog v-model="tutorial.visible" class="typical-dialog" title="产品配置" width="1100px">
      <TutorialPanel
        v-model="tutorial.contextProduct"
        @cancel="tutorial.visible = false"
      ></TutorialPanel>
    </el-dialog>
  </div>
</template>

<style scoped>
.product-list-view {
  :deep() {
    .fund-name-cell {
      width: 100%;
      overflow: hidden;
    }
    .fund-name {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }
    .fund-strategy {
      width: 100%;
      font-size: 12px;
      font-weight: 400;
      color: var(--g-text-color-4);
    }
    .realtime-nav {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
      line-height: 20px;
    }
    .realtime-nav-date {
      line-height: 17px;
      font-size: 12px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }
  }
}
</style>
