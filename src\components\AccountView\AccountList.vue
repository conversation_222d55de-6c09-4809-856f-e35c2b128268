<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { Formatter } from '@/script';
import { AssetType, Repos, type LegacyAccountInfo } from '../../../../xtrade-sdk/dist';

interface CellRenderParam {
  rowData: LegacyAccountInfo;
  cellData: any;
}

const repoInstance = new Repos.GovernanceRepo();
const AssetTypes = Object.values(AssetType);
const aa = {} as any as LegacyAccountInfo;
console.log(aa.assetType);

// 基础列定义
const columns: ColumnDefinition<LegacyAccountInfo> = [
  { key: 'accountName', title: '账号', width: 190, sortable: true },
  { key: 'balance', title: '权益', width: 130, sortable: true, cellRenderer: render2Thousands },
  { key: 'marketValue', title: '市值', width: 130, sortable: true, cellRenderer: render2Thousands },
  {
    key: 'available',
    title: '可用资金',
    width: 130,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'profitRatio',
    title: '收益率',
    width: 100,
    sortable: true,
    cellRenderer: renderPercentage,
  },
  {
    key: 'positionProfit',
    title: '持仓盈亏',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  { key: 'nav', title: '净值', width: 100, sortable: true, cellRenderer: render2Thousands },
  {
    key: 'preBalance',
    title: '昨日权益',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'frozenMargin',
    title: '冻结资金',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'closeProfit',
    title: '平仓盈亏',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanBuyBalance',
    title: '融资买入',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanSellBalance',
    title: '融券卖出',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'commission',
    title: '手续费',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'margin',
    title: '占用保证金',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  { key: 'assetType', title: '类型', width: 100, sortable: true, cellRenderer: renderAccountType },
  { key: 'orgName', title: '机构', width: 100, sortable: true, cellRenderer: render2Thousands },
  {
    key: 'brokerName',
    title: '经纪商',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
];

// 行操作
const rowActions: RowAction<LegacyAccountInfo>[] = [
  {
    label: '配置',
    icon: 'setting',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function render2Thousands(params: CellRenderParam) {
  return <span>{Formatter.thousands(params.cellData as number)}</span>;
}

function renderPercentage(params: CellRenderParam) {
  return <span>{((params.cellData as number) * 100).toFixed(2)}%</span>;
}

function renderAccountType(params: CellRenderParam) {
  return <span>{Formatter.renderLabel(params.cellData as number, AssetTypes)}</span>;
}

function editRow(row: LegacyAccountInfo) {
  console.log('edit', row);
}

function deleteRow(row: LegacyAccountInfo) {
  console.log('delete', row);
}

const records = shallowRef<LegacyAccountInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = (await repoInstance.QueryAccounts()).data || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    search-placeholder="搜索账号"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-height="44"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button>
        <el-button type="primary">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建账号</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
