import type { RoleMenuPermission } from '@/types';

import {
  Repos,
  type <PERSON><PERSON>enu,
  type FormMenu,
  type <PERSON>Role,
  type MomRole,
  type MomPermission,
  type FormPermission,
  type MomUser,
  type FormUser,
  type UserPasswordReset,
  type UserPasswordChange,
} from '../../../xtrade-sdk/dist';

const adminRepo = new Repos.AdminRepo();

class AdminService {
  // ==================== 用户管理相关方法 ====================

  static async getUsers() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryUsers();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createUser(user: FormUser) {
    return adminRepo.CreateUser(user);
  }

  static async updateUser(user: MomUser) {
    return adminRepo.UpdateUser(user);
  }

  static async deleteUser(user_id: number) {
    return adminRepo.DeleteUser(user_id);
  }

  static async forceLogoutUser(user_id: number) {
    return adminRepo.ForceLogoutUser(user_id);
  }

  static async resetUserPassword(data: UserPasswordReset) {
    return adminRepo.ResetUserPassword(data);
  }

  static async changeUserPassword(data: UserPasswordChange) {
    return adminRepo.ChangeUserPassword(data);
  }

  static async getOrgs() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryOrgs();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getRoles() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryRoles();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createRole(role: FormRole) {
    return adminRepo.CreateRole(role);
  }

  static async updateRole(role: MomRole) {
    return adminRepo.UpdateRole(role);
  }

  static async deleteRole(role_id: number) {
    return adminRepo.DeleteRole(role_id);
  }

  static async getMenuTree() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryMenuTree();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getRoleMenuTree(roleId: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryRoleMenu(roleId);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async saveRoleMenuPermissions(role_id: number, data: RoleMenuPermission[]) {
    return adminRepo.SaveRoleMenuPermissions(role_id, data);
  }

  static async getBrokers() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryBrokers();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async getTerminals() {
    const { errorCode, errorMsg, data } = await adminRepo.QueryTerminals();
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  // ==================== 菜单管理相关方法 ====================

  static async createMenu(menu: FormMenu) {
    return adminRepo.CreateMenu(menu);
  }

  static async updateMenu(menu: MomMenu) {
    return adminRepo.UpdateMenu(menu);
  }

  static async deleteMenu(menu_id: number) {
    return adminRepo.DeleteMenu(menu_id);
  }

  // ==================== 权限管理相关方法 ====================

  static async getPermissionsByMenu(menu_id: number) {
    const { errorCode, errorMsg, data } = await adminRepo.QueryPermissionsByMenu(menu_id);
    if (errorCode == 0) {
      return data!;
    } else {
      console.error(errorMsg);
      return [];
    }
  }

  static async createPermission(permission: FormPermission) {
    return adminRepo.CreatePermission(permission);
  }

  static async updatePermission(permission: MomPermission) {
    return adminRepo.UpdatePermission(permission);
  }

  static async deletePermission(permission_id: number) {
    return adminRepo.DeletePermission(permission_id);
  }
}

export default AdminService;
