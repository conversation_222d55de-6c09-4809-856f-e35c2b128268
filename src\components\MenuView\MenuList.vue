<script setup lang="tsx">
import { onMounted, shallowRef } from 'vue';
import { ElMessage, ElMessageBox, ElTree, type TreeNodeData } from 'element-plus';
import type { MomMenu, MomMenuTree } from '../../../../xtrade-sdk/dist';
import { AdminService } from '@/api';
import MenuDialog from './MenuDialog.vue';
import PermissionDialog from './PermissionDialog.vue';

// 菜单树数据
const menuTreeData = shallowRef<MomMenuTree[]>([]);
const selectedMenu = shallowRef<MomMenuTree | undefined>();

// 对话框状态
const menuDialogVisible = shallowRef(false);
const permissionDialogVisible = shallowRef(false);
const editingMenu = shallowRef<MomMenu | undefined>();
const parentMenuId = shallowRef<number | undefined>();

// 树形组件引用
const treeRef = shallowRef<InstanceType<typeof ElTree>>();

// 搜索文本
const searchText = shallowRef('');

// 树形配置
const treeProps = {
  children: 'children',
  label: 'menuName',
  value: 'id',
};

// 过滤树节点
const filterNode = (value: string, data: TreeNodeData) => {
  if (!value) return true;
  return data.menuName.includes(value);
};

// 处理搜索
const handleSearch = (value: string) => {
  treeRef.value?.filter(value);
};

// 展开所有节点
const expandAll = () => {
  const nodes = treeRef.value?.store.nodesMap;
  for (const key in nodes) {
    nodes[key].expanded = true;
  }
};

// 折叠所有节点
const collapseAll = () => {
  const nodes = treeRef.value?.store.nodesMap;
  for (const key in nodes) {
    nodes[key].expanded = false;
  }
};

// 加载菜单树数据
const loadMenuTree = async () => {
  try {
    const data = await AdminService.getMenuTree();
    menuTreeData.value = data;
  } catch (error) {
    ElMessage.error('加载菜单数据失败');
    console.error(error);
  }
};

// 处理节点点击
const handleNodeClick = (data: MomMenuTree) => {
  selectedMenu.value = data;
};

// 创建菜单
const handleCreateMenu = (parentId?: number) => {
  editingMenu.value = undefined;
  parentMenuId.value = parentId;
  menuDialogVisible.value = true;
};

// 编辑菜单
const handleEditMenu = (menu: MomMenuTree) => {
  editingMenu.value = menu;
  parentMenuId.value = menu.parentMenuId;
  menuDialogVisible.value = true;
};

// 删除菜单
const handleDeleteMenu = async (menu: MomMenuTree) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单"${menu.menuName}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    await AdminService.deleteMenu(menu.id);
    ElMessage.success('删除成功');
    await loadMenuTree();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
      console.error(error);
    }
  }
};

// 权限管理
const handlePermissionManagement = (menu: MomMenuTree) => {
  selectedMenu.value = menu;
  permissionDialogVisible.value = true;
};

// 菜单保存成功回调
const handleMenuSaved = () => {
  menuDialogVisible.value = false;
  loadMenuTree();
};

// 权限保存成功回调
const handlePermissionSaved = () => {
  permissionDialogVisible.value = false;
};

// 渲染树节点
const renderTreeNode = ({ data }: { data: MomMenuTree }) => {
  return (
    <div class="tree-node flex aic jcsb w-full">
      <div class="flex aic">
        <span>{data.menuName}</span>
      </div>
      <div class="node-actions flex aic gap-4">
        <el-button
          size="small"
          type="primary"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handleCreateMenu(data.id);
          }}
        >
          添加
        </el-button>
        <el-button
          size="small"
          type="primary"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handleEditMenu(data);
          }}
        >
          修改
        </el-button>
        <el-button
          size="small"
          type="primary"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handlePermissionManagement(data);
          }}
        >
          权限
        </el-button>
        <el-button
          size="small"
          type="danger"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handleDeleteMenu(data);
          }}
        >
          删除
        </el-button>
      </div>
    </div>
  );
};

onMounted(() => {
  loadMenuTree();
});
</script>

<template>
  <div class="menu-management" h-full flex flex-col>
    <!-- 头部操作栏 -->
    <div class="header-actions" flex aic jcsb px-15 py-11 bg="[--g-block-bg-5]">
      <div flex aic gap-8>
        <el-input
          class="typical-search-box"
          v-model="searchText"
          placeholder="搜索菜单"
          clearable
          style="width: 200px"
          @input="handleSearch"
        />
        <el-button size="small" @click="expandAll">展开所有</el-button>
        <el-button size="small" @click="collapseAll">折叠所有</el-button>
      </div>
      <div flex aic gap-8>
        <!-- <el-button @click="loadMenuTree">
          <i class="iconfont icon-refresh" mr-4></i>
          刷新
        </el-button> -->
        <el-button type="primary" @click="handleCreateMenu()">
          <i class="iconfont icon-add-new" mr-4></i>
          创建菜单
        </el-button>
      </div>
    </div>

    <!-- 菜单树 -->
    <div class="tree-container" flex-1 p-16 overflow-auto>
      <el-tree
        ref="treeRef"
        :data="menuTreeData"
        :props="treeProps"
        :filter-node-method="filterNode"
        node-key="id"
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <component :is="renderTreeNode" :node="node" :data="data" />
        </template>
      </el-tree>
    </div>

    <!-- 菜单编辑对话框 -->
    <MenuDialog
      v-model:visible="menuDialogVisible"
      :menu="editingMenu"
      :parent-menu-id="parentMenuId"
      :menu-tree="menuTreeData"
      @saved="handleMenuSaved"
    />

    <!-- 权限管理对话框 -->
    <PermissionDialog
      v-model:visible="permissionDialogVisible"
      :menu="selectedMenu"
      @saved="handlePermissionSaved"
    />
  </div>
</template>

<style scoped>
.menu-management {
  background: var(--g-bg-1);
}

.tree-node {
  padding: 4px 8px;
  border-radius: 4px;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree-node__expand-icon) {
  padding: 6px;
}
</style>
