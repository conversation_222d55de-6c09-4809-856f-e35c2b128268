<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { Repos, type MomRole } from '../../../../xtrade-sdk/dist';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';

const repoInstance = new Repos.AdminRepo();

type FormRole = Pick<MomRole, 'roleName' | 'description'>;

const { role } = defineProps<{
  role?: MomRole;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormRole>({
  roleName: '',
  description: '',
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (role) {
      form.value = {
        roleName: role.roleName,
        description: role.description,
      };
    }
  }
});

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (role) {
        const { errorCode, errorMsg } = await repoInstance.UpdateRole({
          ...role,
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('修改成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      } else {
        const { errorCode, errorMsg } = await repoInstance.CreateRole({
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('添加成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      }
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  form.value = {
    roleName: '',
    description: '',
  };
  formRef.value?.resetFields();
};
</script>
<template>
  <el-dialog
    :model-value="visible"
    :title="role ? '修改角色' : '新建角色'"
    width="350px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="90px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="form.roleName" placeholder="请输入角色名称" />
      </el-form-item>
      <el-form-item label="角色描述" prop="description">
        <el-input v-model="form.description" :rows="3" type="textarea" placeholder="角色描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>
